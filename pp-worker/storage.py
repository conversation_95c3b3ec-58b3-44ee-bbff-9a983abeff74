from abc import ABC, abstractmethod
import os
import requests
from supabase import create_client, Client
import json
import logging # <-- Added import
import shutil
import fcntl
import time
from config import service_key, url
class StorageInterface(ABC):
    @abstractmethod
    def get_files(self):
        pass

    @abstractmethod
    def put_files(self):
        pass

class SuperbaseStorage(StorageInterface):
    def __init__(self, bucket_name, local_dir='./tmp', result_dir=f"/output", user_id=None):
        self.local_dir = local_dir
        self.result_dir = local_dir +  result_dir
        self.bucket_name = bucket_name
        self.user_id = user_id
        if not os.path.exists(self.local_dir):
            os.makedirs(self.local_dir)
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
        self.supabase = create_client(url, service_key)

        # Cache configuration
        self.cache_base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache')
        self.cache_enabled = True and user_id is not None  # Only enable cache if user_id is provided

    def _extract_file_id_from_path(self, file_path):
        """
        Extract file ID from a file path.
        Assumes file paths follow pattern: /{user_id}/files/{file_id}/{filename}
        """
        try:
            path_parts = file_path.strip('/').split('/')
            if len(path_parts) >= 3 and path_parts[1] == 'files':
                return path_parts[2]  # file_id
            return None
        except Exception as e:
            logging.warning(f"Could not extract file ID from path '{file_path}': {e}")
            return None

    def _get_cache_dir(self, user_id, file_id):
        """Get the cache directory path for a specific user and file ID."""
        return os.path.join(self.cache_base_dir, str(user_id), str(file_id))

    def _get_cache_file_path(self, user_id, file_id, original_filename):
        """Get the full cache file path."""
        cache_dir = self._get_cache_dir(user_id, file_id)
        return os.path.join(cache_dir, original_filename)

    def _ensure_cache_dir(self, cache_dir):
        """
        Ensure cache directory exists with thread-safe creation.
        Uses file locking to prevent race conditions.
        """
        if os.path.exists(cache_dir):
            return True

        try:
            # Create parent directories first
            parent_dir = os.path.dirname(cache_dir)
            if parent_dir and not os.path.exists(parent_dir):
                os.makedirs(parent_dir, exist_ok=True)

            # Use a lock file to prevent race conditions
            lock_file = cache_dir + '.lock'
            with open(lock_file, 'w') as lock_fd:
                try:
                    fcntl.flock(lock_fd.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                    if not os.path.exists(cache_dir):
                        os.makedirs(cache_dir, exist_ok=True)
                        logging.info(f"Created cache directory: {cache_dir}")
                except IOError:
                    # Another process is creating the directory, wait briefly
                    time.sleep(0.1)
                    if not os.path.exists(cache_dir):
                        os.makedirs(cache_dir, exist_ok=True)
                finally:
                    fcntl.flock(lock_fd.fileno(), fcntl.LOCK_UN)

            # Clean up lock file
            try:
                os.remove(lock_file)
            except OSError:
                pass  # Lock file might have been removed by another process

            return True
        except Exception as e:
            logging.error(f"Failed to create cache directory {cache_dir}: {e}")
            return False

    def _copy_from_cache(self, cache_file_path, local_file_path):
        """Copy file from cache to local directory."""
        try:
            if os.path.exists(cache_file_path):
                shutil.copy2(cache_file_path, local_file_path)
                logging.info(f"Copied from cache: {cache_file_path} -> {local_file_path}")
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to copy from cache {cache_file_path} to {local_file_path}: {e}")
            return False

    def _save_to_cache(self, local_file_path, cache_file_path):
        """Save file to cache after successful download."""
        try:
            cache_dir = os.path.dirname(cache_file_path)
            if self._ensure_cache_dir(cache_dir):
                shutil.copy2(local_file_path, cache_file_path)
                logging.info(f"Saved to cache: {local_file_path} -> {cache_file_path}")
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to save to cache {local_file_path} to {cache_file_path}: {e}")
            return False
    """
    def get_files(self, files):
        for file in files:
            url = self.supabase.storage.from_(bucket_name).create_signed_url(file,60)["signedURL"]
            filename = os.path.basename(file)
            response = requests.get(url, stream=True)
            if response.status_code != 200:
                print(f"Failed to download {file}. Status code: {response.status_code}")
                continue
            with open(os.path.join(self.local_dir, filename), 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:  # filter out keep-alive new chunks
                        f.write(chunk)"""
    # Original get_files for standard jobs with caching support
    def get_files(self, files, folder):
        """
        Downloads files relative to a folder path from the Supabase bucket.
        Used for standard jobs. Includes caching support for standard tasks only.

        Args:
            files (list[str]): A list of relative file paths or basenames.
            folder (str): The remote folder path prefix within the bucket.

        Returns:
            list[str]: A list of local paths where files were successfully downloaded.
        """
        downloaded_files = []
        if not files:
            logging.warning("Standard get_files called with empty files list.")
            return downloaded_files

        for file in files: # 'file' here is likely a relative path or basename
            local_file_path = None # Define outside try block for cleanup
            remote_file_path = None
            try:
                # Construct the full remote path
                remote_file_path = f"{folder}/{file}".replace('//', '/') # Basic path joining
                # Handle case where folder might be empty or root '/'
                if remote_file_path.startswith('/'):
                     remote_file_path = remote_file_path.lstrip('/')

                if not remote_file_path:
                    logging.warning(f"Skipping download due to empty remote path derived from folder='{folder}', file='{file}'")
                    continue

                # Save using basename to the instance's local_dir
                filename = os.path.basename(file) # Use basename of the input 'file'
                local_file_path = os.path.join(self.local_dir, filename)

                # Try cache first if enabled
                cache_hit = False
                if self.cache_enabled:
                    file_id = self._extract_file_id_from_path(remote_file_path)
                    if file_id:
                        cache_file_path = self._get_cache_file_path(self.user_id, file_id, filename)
                        if self._copy_from_cache(cache_file_path, local_file_path):
                            downloaded_files.append(local_file_path)
                            cache_hit = True
                            continue

                # If cache miss or cache disabled, download from remote
                url_response = self.supabase.storage.from_(self.bucket_name).create_signed_url(remote_file_path, 60)

                if "signedURL" not in url_response or not url_response["signedURL"]:
                     logging.error(f"Failed to create signed URL for {remote_file_path}. Response: {url_response}")
                     continue

                signed_url = url_response["signedURL"]

                logging.info(f"Attempting standard download: {remote_file_path} -> {local_file_path}")
                response = requests.get(signed_url, stream=True)
                response.raise_for_status()

                with open(local_file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                downloaded_files.append(local_file_path)
                logging.info(f"Successfully downloaded {remote_file_path} to {local_file_path}")

                # Save to cache after successful download if enabled
                if self.cache_enabled and not cache_hit:
                    file_id = self._extract_file_id_from_path(remote_file_path)
                    if file_id:
                        cache_file_path = self._get_cache_file_path(self.user_id, file_id, filename)
                        self._save_to_cache(local_file_path, cache_file_path)

            except requests.exceptions.RequestException as e:
                 logging.error(f"HTTP error downloading {remote_file_path or file}: {e}")
            except Exception as e:
                 logging.error(f"Failed to download or save {remote_file_path or file}: {e}", exc_info=True)
                 if local_file_path and os.path.exists(local_file_path):
                     try: os.remove(local_file_path)
                     except OSError as rm_err: logging.error(f"Error cleaning up partial download {local_file_path}: {rm_err}")

        return downloaded_files

    # Renamed method for bulk jobs and standard jobs
    def get_files_bulk(self, files_map: dict[str, str], enable_caching: bool = False):
        """
        Downloads files from the Supabase bucket using their full remote paths
        and saves them to specified unique local paths. Used for bulk jobs and standard jobs.

        Args:
            files_map (dict[str, str]): A dictionary mapping full remote file paths
                                        (keys) to desired unique absolute local file paths (values).
            enable_caching (bool): Whether to enable caching for this download operation.
                                   Should be True for standard tasks, False for bulk tasks.

        Returns:
            list[str]: A list of local paths where files were successfully downloaded.
        """
        downloaded_files = []
        if not files_map:
            logging.warning("get_files_bulk called with an empty files_map.")
            return downloaded_files

        for remote_file_path, desired_local_path in files_map.items():
            try:
                local_dir = os.path.dirname(desired_local_path)
                if not os.path.exists(local_dir):
                    os.makedirs(local_dir, exist_ok=True)

                # Try cache first if caching is enabled and cache is available
                cache_hit = False
                if enable_caching and self.cache_enabled:
                    file_id = self._extract_file_id_from_path(remote_file_path)
                    if file_id:
                        # Extract original filename from the desired local path
                        original_filename = os.path.basename(remote_file_path)
                        cache_file_path = self._get_cache_file_path(self.user_id, file_id, original_filename)
                        if self._copy_from_cache(cache_file_path, desired_local_path):
                            downloaded_files.append(desired_local_path)
                            cache_hit = True
                            continue

                # If cache miss or caching disabled, download from remote
                url_response = self.supabase.storage.from_(self.bucket_name).create_signed_url(remote_file_path, 60)

                if "signedURL" not in url_response or not url_response["signedURL"]:
                     logging.error(f"Failed to create signed URL for {remote_file_path}. Response: {url_response}")
                     continue

                signed_url = url_response["signedURL"]

                logging.info(f"Attempting bulk download: {remote_file_path} -> {desired_local_path}")
                response = requests.get(signed_url, stream=True)
                response.raise_for_status()

                with open(desired_local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                downloaded_files.append(desired_local_path)
                logging.info(f"Successfully downloaded {remote_file_path} to {desired_local_path}")

                # Save to cache after successful download if caching is enabled and not a cache hit
                if enable_caching and self.cache_enabled and not cache_hit:
                    file_id = self._extract_file_id_from_path(remote_file_path)
                    if file_id:
                        original_filename = os.path.basename(remote_file_path)
                        cache_file_path = self._get_cache_file_path(self.user_id, file_id, original_filename)
                        self._save_to_cache(desired_local_path, cache_file_path)

            except requests.exceptions.RequestException as e:
                 logging.error(f"HTTP error downloading {remote_file_path}: {e}")
            except OSError as e:
                 logging.error(f"OS error creating directory or writing file for {desired_local_path}: {e}")
            except Exception as e:
                 logging.error(f"Failed to download or save {remote_file_path} to {desired_local_path}: {e}", exc_info=True)
                 if os.path.exists(desired_local_path):
                     try: os.remove(desired_local_path)
                     except OSError as rm_err: logging.error(f"Error cleaning up partial download {desired_local_path}: {rm_err}")

        return downloaded_files

    def put_files(self, remoteoutputfolder):
        files = os.listdir(self.result_dir)
        output_files = []
        for file in files:
            filename = os.path.basename(file)
            remotefilepath = remoteoutputfolder + filename
            output_files.append(remotefilepath)
            uploadurl= self.supabase.storage.from_(self.bucket_name).create_signed_upload_url(remotefilepath)["signed_url"]
            if os.path.isdir(os.path.join(self.result_dir, file)):
                continue
            with open(os.path.join(self.result_dir, file), 'rb') as f:
                requests.put(uploadurl, data=f)
        return output_files
        

class LocalMountedStorage(StorageInterface):
    def __init__(self, source_dir, local_dir, result_dir=f"/output"):
        self.local_dir = local_dir
        self.result_dir = local_dir +  result_dir
        if not os.path.exists(self.local_dir):
            os.makedirs(self.local_dir)
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
        #mount sorucedir to localdir
        if not os.path.ismount(self.local_dir):
            os.system(f"mount --bind {source_dir} {self.local_dir}")
        
    def __del__(self):
        #unmount localdir
        if os.path.ismount(self.local_dir):
            os.system(f"umount {self.local_dir}")


    def get_files(self, files):
        return files

    def put_files(self):
        files = os.listdir(self.result_dir)
        return files
    

class  CopyLocalStorage(StorageInterface):
    def __init__(self, source_dir, local_dir, result_dir=f"/output"):
        self.source_dir = source_dir
        self.local_dir = local_dir
        self.result_dir = local_dir +  result_dir
        if not os.path.exists(self.local_dir):
            os.makedirs(self.local_dir)
        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)
        
    

    def get_files(self, files):
        #copy files from source_dir to local_dir
        for file in files:
            os.makedirs(os.path.dirname(os.path.join(self.local_dir, file)), exist_ok=True)
            os.system(f"cp -r {os.path.join(self.source_dir, file)} {os.path.join(self.local_dir, file)}")
        return files

    def put_files(self, remoteoutputfolder):
        
        #copy files from result_dir to remoteoutputfolder
        os.system(f"cp -r {self.result_dir}/* {remoteoutputfolder}")
        files = os.listdir(remoteoutputfolder)
        return files